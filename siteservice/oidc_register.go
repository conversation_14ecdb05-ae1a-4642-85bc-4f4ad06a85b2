package siteservice

import (
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"git.gig.tech/gig-meneja/iam/credentials/password"
	"git.gig.tech/gig-meneja/iam/db"
	"git.gig.tech/gig-meneja/iam/db/user"
	validationdb "git.gig.tech/gig-meneja/iam/db/validation"
	"github.com/gorilla/sessions"
	log "github.com/sirupsen/logrus"
)

// OIDCRegisterInfo is the response data for GetOIDCRegisterInfo
type OIDCRegisterInfo struct {
	Email         string `json:"email"`
	EmailVerified bool   `json:"email_verified"`
	FirstName     string `json:"firstname"`
	LastName      string `json:"lastname"`
	ProviderID    string `json:"provider_id"`
	Sub           string `json:"sub"`
}

// OIDCRegisterRequest is the request body for RegisterOIDCUser
type OIDCRegisterRequest struct {
	FirstName           string `json:"firstname"`
	LastName            string `json:"lastname"`
	Email               string `json:"email"`
	UsePassword         bool   `json:"use_password"`
	Password            string `json:"password,omitempty"`
	AcceptanceTimestamp *int64 `json:"acceptance_timestamp"`
	PPVersion           *int   `json:"privacy_policy_version"`
	CPVersion           *int   `json:"cookie_policy_version"`
	AUPVersion          *int   `json:"acceptable_use_policy_version"`
	TCVersion           *int   `json:"terms_and_conditions_version"`
}

// GetOIDCRegisterInfo returns the OIDC information stored in the session for registration
func (service *Service) GetOIDCRegisterInfo(w http.ResponseWriter, r *http.Request) {
	loginSession, err := service.GetSession(r, SessionLogin, "loginsession")
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Check if the session is new or missing required data
	if loginSession.IsNew {
		http.Error(w, "No active OIDC registration session", http.StatusUnauthorized)
		return
	}

	// Extract data from session
	providerID, _ := loginSession.Values["oidc_provider"].(string)
	sub, _ := loginSession.Values["oidc_sub"].(string)
	email, _ := loginSession.Values["oidc_email"].(string)
	emailVerified, _ := loginSession.Values["oidc_email_verified"].(bool)
	firstName, _ := loginSession.Values["oidc_firstname"].(string)
	lastName, _ := loginSession.Values["oidc_lastname"].(string)

	// Verify that we have the necessary data
	if providerID == "" || sub == "" {
		http.Error(w, "Missing OIDC provider information", http.StatusBadRequest)
		return
	}

	// Return the information
	info := OIDCRegisterInfo{
		Email:         email,
		EmailVerified: emailVerified,
		FirstName:     firstName,
		LastName:      lastName,
		ProviderID:    providerID,
		Sub:           sub,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(info)
}

// RegisterOIDCUser handles the registration of a user from OIDC data
func (service *Service) RegisterOIDCUser(w http.ResponseWriter, r *http.Request) {
	// Parse request body
	var registerData OIDCRegisterRequest
	if err := json.NewDecoder(r.Body).Decode(&registerData); err != nil {
		log.Error("Error decoding OIDC registration request:", err)
		http.Error(w, http.StatusText(http.StatusBadRequest), http.StatusBadRequest)
		return
	}

	// Validate the registration data
	shouldReturn := ValidateOIDCRegister(registerData, w)
	if shouldReturn {
		return
	}

	// Get OIDC data from session
	loginSession, err := service.GetSession(r, SessionLogin, "loginsession")
	if err != nil {
		log.Error(err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Extract data from session
	providerID, _ := loginSession.Values["oidc_provider"].(string)
	sub, _ := loginSession.Values["oidc_sub"].(string)
	email, _ := loginSession.Values["oidc_email"].(string)
	emailVerified, _ := loginSession.Values["oidc_email_verified"].(bool)

	// Verify that we have the necessary data
	if providerID == "" || sub == "" {
		http.Error(w, "Missing OIDC provider information", http.StatusBadRequest)
		return
	}

	// Create the user
	userMgr := user.NewManager(r)
	valMgr := validationdb.NewManager(r)

	// Check if a user with the same OIDC identity already exists
	existingUser, err := userMgr.GetByOIDCProviderAndSub(providerID, sub)
	if err == nil && existingUser != nil {
		log.Error("User with the same OIDC identity already exists")
		writeErrorResponse(w, "User with same oidc identity exists", http.StatusConflict)
		return
	}

	// Check if a user with the same email already exists
	if email != "" {
		usernames, err := userMgr.GetByEmailAddress(email)
		if err != nil && !db.IsNotFound(err) {
			log.Error("Error checking for existing email:", err)
			http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
			return
		}
		if len(usernames) > 0 {
			log.Error("User with the same email already exists")
			writeErrorResponse(w, "User with same email exists", http.StatusConflict)
			return
		}
	}

	// Generate the username from the firstname and lastname
	username, err := generateUsername(r, registerData.FirstName, registerData.LastName)
	if err != nil {
		log.Error("Failed to generate username: ", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Create the user object
	log.Debug("Creating new user with username ", username)
	newUser := &user.User{
		Username:               username,
		Firstname:              registerData.FirstName,
		Lastname:               registerData.LastName,
		PPAcceptanceTimestamp:  *registerData.AcceptanceTimestamp,
		PPVersion:              *registerData.PPVersion,
		CPAcceptanceTimestamp:  *registerData.AcceptanceTimestamp,
		CPVersion:              *registerData.CPVersion,
		AUPAcceptanceTimestamp: *registerData.AcceptanceTimestamp,
		AUPVersion:             *registerData.AUPVersion,
		TCAcceptanceTimestamp:  *registerData.AcceptanceTimestamp,
		TCVersion:              *registerData.TCVersion,
		OIDCIdentities: []user.OIDCIdentity{
			{
				Provider:  providerID,
				Subject:   sub,
				LastLogin: time.Now(),
			},
		},
	}

	// Add email if available
	if email != "" {
		emailLabel := "Primary"
		newUser.EmailAddresses = append(newUser.EmailAddresses,
			user.EmailAddress{Label: emailLabel, EmailAddress: email})

		// Mark as verified if provider states it's verified
		if emailVerified {
			validatedEmail := valMgr.NewValidatedEmailAddress(username, email)
			if err := valMgr.SaveValidatedEmailAddress(validatedEmail); err != nil {
				log.Warningf("Failed to save validated email: %v", err)
			}
		}
	}

	// Save the user
	if err := userMgr.Save(newUser); err != nil {
		log.Error("Error saving user:", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Add password if requested
	if registerData.UsePassword {
		log.Debug("Saving user password")
		pwdMgr := password.NewManager(r)
		if err := pwdMgr.Save(username, registerData.Password); err != nil {
			log.Error("Error saving password:", err)
			http.Error(w, "Failed to set password", http.StatusInternalServerError)
			return
		}
	}

	// service.loginUser(w, r, username)

	// Set up the login session
	loginSession.Values["username"] = username

	// Set up the OAuth session for the new user
	if err := service.SetLoggedInUser(w, r, username); err != nil {
		log.Error("Failed to set up OAuth session:", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Save the session
	if err := sessions.Save(r, w); err != nil {
		log.Error("Failed to save session:", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	// Return success response
	response := struct {
		Username    string `json:"username"`
		RedirectURL string `json:"redirecturl"`
	}{
		Username:    username,
		RedirectURL: "/",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// ValidateOIDCRegister validates the registration data for an OIDC user
func ValidateOIDCRegister(registerData OIDCRegisterRequest, w http.ResponseWriter) bool {
	if registerData.AcceptanceTimestamp == nil {
		writeErrorResponse(w, "missing_acceptance_timestamp", http.StatusBadRequest)
		return true
	}
	if registerData.CPVersion == nil {
		writeErrorResponse(w, "missing_cookie_policy_version", http.StatusBadRequest)
		return true
	}
	if registerData.PPVersion == nil {
		writeErrorResponse(w, "missing_privacy_policy_version", http.StatusBadRequest)
		return true
	}
	if registerData.AUPVersion == nil {
		writeErrorResponse(w, "missing_acceptable_use_policy_version", http.StatusBadRequest)
		return true
	}
	if registerData.TCVersion == nil {
		writeErrorResponse(w, "missing_terms_and_conditions_version", http.StatusBadRequest)
		return true
	}

	// Check the users first name
	if !user.ValidateName(strings.ToLower(registerData.FirstName)) {
		log.Debug("User first name invalid")
		writeErrorResponse(w, "invalid_first_name", http.StatusUnprocessableEntity)
		return true
	}

	// Check the users last name
	if !user.ValidateName(strings.ToLower(registerData.LastName)) {
		log.Debug("User last name invalid")
		writeErrorResponse(w, "invalid_last_name", http.StatusUnprocessableEntity)
		return true
	}

	if registerData.UsePassword {
		// Check the password
		if err := password.Check(registerData.Password); err != nil {
			if err != password.ErrInvalidPassword {
				log.Error("Failed to verify password: ", err)
				http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
				return true
			}
			log.Debug("User password is invalid")
			writeErrorResponse(w, "invalid_password", http.StatusUnprocessableEntity)
			return true
		}
	}
	return false
}
